# UI设计方案
## 机器视觉筛选程序界面设计

### 1. 设计概述

#### 1.1 设计原则
- **简洁高效**：界面布局清晰，操作流程简化
- **工业化风格**：适合工业环境使用，视觉效果专业
- **实时反馈**：状态信息实时更新，操作结果即时显示
- **容错设计**：防误操作设计，异常情况友好提示

#### 1.2 技术框架
- **UI框架**：WPF (.NET 8.0)
- **设计模式**：MVVM
- **UI库**：Material Design + 自定义控件
- **图像显示**：自定义Halcon图像控件

### 2. 主界面布局设计

#### 2.1 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                        标题栏 + 菜单栏                        │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│             │                                               │
│   左侧导航   │                 主工作区                      │
│   功能面板   │              (动态内容区域)                    │
│             │                                               │
│             │                                               │
├─────────────┴───────────────────────────────────────────────┤
│                        底部状态栏                            │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2 顶部区域设计
- **标题栏**：
  - 应用程序名称和版本号
  - 窗口控制按钮（最小化、最大化、关闭）
  - 激活状态指示器
- **菜单栏**：
  - 文件：导入/导出配置、退出
  - 设置：系统设置、激活管理
  - 工具：日志查看、数据管理
  - 帮助：使用说明、关于

#### 2.3 左侧导航面板
- **功能模块导航**：
  ```
  📷 相机设置
  📋 模板管理  
  ⚙️ 筛选运行
  📊 数据统计
  📝 日志查看
  🔧 系统设置
  ```
- **快速状态显示**：
  - 相机连接状态
  - PLC通信状态
  - 当前运行模式
  - 激活剩余时间（试用版）

#### 2.4 底部状态栏
- **左侧状态信息**：
  - 系统运行状态
  - 当前操作提示
  - 错误/警告信息
- **右侧系统信息**：
  - 当前时间
  - 系统资源使用情况
  - 网络连接状态

### 3. 功能模块界面设计

#### 3.1 相机设置界面

##### 3.1.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   相机连接配置   │              实时预览区域                │
│                 │                                         │
├─────────────────┤                                         │
│                 │                                         │
│   参数调节区域   │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   拍照控制区域   │              拍照结果显示                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.1.2 功能区域详细设计

**相机连接配置区域**：
- IP地址输入框
- 端口号设置
- 连接/断开按钮
- 连接状态指示灯
- 自动搜索相机按钮

**参数调节区域**：
- 曝光时间滑块 (μs)
- 增益调节滑块 (0-15)
- 分辨率显示
- 帧率显示
- 参数重置按钮

**实时预览区域**：
- Halcon图像显示控件
- 缩放工具栏
- 十字线显示开关
- 图像信息显示

**拍照控制区域**：
- 单次拍照按钮
- 连续拍照开关
- 保存图像按钮
- 图像格式选择

#### 3.2 模板管理界面

##### 3.2.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   模板列表区域   │              图像显示区域                │
│                 │                                         │
├─────────────────┤                                         │
│                 │                                         │
│   模板操作区域   │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   ROI工具区域    │              特征识别结果                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.2.2 功能区域详细设计

**模板列表区域**：
- 模板缩略图列表
- 模板名称和创建时间
- 模板状态指示
- 搜索和筛选功能

**模板操作区域**：
- 新建模板按钮
- 编辑模板按钮
- 删除模板按钮
- 导入/导出按钮
- 复制模板按钮

**图像显示区域**：
- 当前图像显示
- ROI区域高亮显示
- 识别结果叠加显示
- 缩放和平移工具

**ROI工具区域**：
- 矩形ROI工具
- 圆形ROI工具
- 多边形ROI工具
- ROI编辑工具
- 清除ROI按钮

**特征识别结果区域**：
- 轮廓识别结果
- 数字编码位置
- 特征参数显示
- 识别置信度

#### 3.3 筛选运行界面

##### 3.3.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   运行控制区域   │              实时图像显示                │
│                 │                                         │
├─────────────────┤                                         │
│                 │                                         │
│   参数设置区域   │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   统计信息区域   │              处理结果显示                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.3.2 功能区域详细设计

**运行控制区域**：
- 开始运行按钮
- 暂停运行按钮
- 停止运行按钮
- 运行状态指示
- 紧急停止按钮

**参数设置区域**：
- 模板选择下拉框
- 匹配阈值滑块
- 拍照间隔设置
- 检测区域显示开关
- 参数保存按钮

**实时图像显示区域**：
- 当前检测图像
- 匹配结果叠加
- 检测区域标注
- 结果状态显示

**统计信息区域**：
- 总检测数量
- 合格产品数量
- 不合格产品数量
- 合格率统计
- 平均处理时间

**处理结果显示区域**：
- 最近检测结果列表
- 结果详细信息
- 匹配度数值
- 时间戳信息

#### 3.4 数据统计界面

##### 3.4.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   时间筛选区域   │              统计图表区域                │
│                 │                                         │
├─────────────────┤                                         │
│                 │                                         │
│   统计类型选择   │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   数据导出区域   │              详细数据列表                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.4.2 功能区域详细设计

**时间筛选区域**：
- 开始时间选择器
- 结束时间选择器
- 快速时间选择（今天、昨天、本周、本月）
- 查询按钮
- 重置按钮

**统计类型选择区域**：
- 生产统计选项
- 质量统计选项
- 效率统计选项
- 异常统计选项

**统计图表区域**：
- 折线图（趋势分析）
- 柱状图（数量对比）
- 饼图（比例分析）
- 图表切换工具

**详细数据列表区域**：
- 分页数据表格
- 排序功能
- 筛选功能
- 详情查看

**数据导出区域**：
- 导出格式选择
- 导出范围设置
- 导出按钮
- 导出历史

#### 3.5 日志查看界面

##### 3.5.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   日志类型选择   │              日志内容显示                │
│                 │                                         │
├─────────────────┤                                         │
│                 │                                         │
│   筛选条件设置   │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   日志管理操作   │              日志详细信息                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.5.2 功能区域详细设计

**日志类型选择区域**：
- 生产日志选项
- 程序日志选项
- 错误日志选项
- 操作日志选项

**筛选条件设置区域**：
- 时间范围选择
- 日志级别筛选
- 关键字搜索
- 模块筛选

**日志内容显示区域**：
- 日志列表表格
- 实时日志滚动
- 日志级别颜色标识
- 分页控制

**日志详细信息区域**：
- 选中日志的详细内容
- 异常堆栈信息
- 相关上下文信息
- 复制和导出功能

**日志管理操作区域**：
- 清理历史日志
- 导出日志文件
- 日志配置设置
- 自动刷新开关

#### 3.6 系统设置界面

##### 3.6.1 布局结构
```
┌─────────────────┬─────────────────────────────────────────┐
│                 │                                         │
│   设置分类导航   │              设置内容区域                │
│                 │                                         │
│                 │                                         │
│                 │                                         │
│                 │                                         │
├─────────────────┼─────────────────────────────────────────┤
│   操作按钮区域   │              设置预览区域                │
└─────────────────┴─────────────────────────────────────────┘
```

##### 3.6.2 功能区域详细设计

**设置分类导航区域**：
- 相机设置
- 通信设置
- 系统设置
- 激活管理
- 界面设置

**设置内容区域**：
- 动态加载对应设置页面
- 参数输入控件
- 设置说明文本
- 默认值恢复按钮

**操作按钮区域**：
- 保存设置按钮
- 取消修改按钮
- 应用设置按钮
- 重置默认按钮

**设置预览区域**：
- 设置修改预览
- 影响范围说明
- 重启提示信息

### 4. 自定义控件设计

#### 4.1 Halcon图像显示控件
- **功能特性**：
  - 支持Halcon图像格式显示
  - 缩放、平移、旋转操作
  - ROI绘制和编辑
  - 图像叠加显示
  - 鼠标坐标显示

#### 4.2 状态指示控件
- **功能特性**：
  - 多状态颜色指示
  - 动画效果支持
  - 状态文本显示
  - 点击事件处理

#### 4.3 参数调节控件
- **功能特性**：
  - 滑块和数值输入结合
  - 实时参数预览
  - 参数范围限制
  - 单位显示支持

### 5. API接口说明

#### 5.1 相机控制接口
```csharp
// 相机连接管理
interface ICameraService
{
    Task<bool> ConnectAsync(string ipAddress, int port);
    Task DisconnectAsync();
    bool IsConnected { get; }
    event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;
}

// 图像采集接口
interface IImageAcquisitionService
{
    Task<HalconImage> CaptureImageAsync();
    void StartLiveView();
    void StopLiveView();
    event EventHandler<ImageCapturedEventArgs> ImageCaptured;
}

// 相机参数控制接口
interface ICameraParameterService
{
    Task SetExposureTimeAsync(double exposureTime);
    Task SetGainAsync(int gain);
    Task<CameraParameters> GetParametersAsync();
    Task SetParametersAsync(CameraParameters parameters);
}
```

#### 5.2 图像处理接口
```csharp
// 图像处理服务接口
interface IImageProcessingService
{
    Task<ProcessingResult> ProcessImageAsync(HalconImage image, Template template);
    Task<List<Contour>> DetectContoursAsync(HalconImage image, ROI roi);
    Task<DigitalCodeInfo> DetectDigitalCodeAsync(HalconImage image, ROI roi);
    Task<double> CalculateMatchScoreAsync(HalconImage image, Template template);
}

// ROI管理接口
interface IROIService
{
    ROI CreateRectangleROI(Point startPoint, Point endPoint);
    ROI CreateCircleROI(Point center, double radius);
    ROI CreatePolygonROI(List<Point> points);
    void EditROI(ROI roi);
    bool IsPointInROI(Point point, ROI roi);
}
```

#### 5.3 模板管理接口
```csharp
// 模板管理服务接口
interface ITemplateService
{
    Task<List<Template>> GetAllTemplatesAsync();
    Task<Template> GetTemplateByIdAsync(int templateId);
    Task<int> SaveTemplateAsync(Template template);
    Task<bool> DeleteTemplateAsync(int templateId);
    Task<bool> UpdateTemplateAsync(Template template);
    Task<bool> ExportTemplateAsync(int templateId, string filePath);
    Task<Template> ImportTemplateAsync(string filePath);
}

// 模板匹配接口
interface ITemplateMatchingService
{
    Task<MatchResult> MatchTemplateAsync(HalconImage image, Template template);
    Task<List<MatchResult>> MatchMultipleTemplatesAsync(HalconImage image, List<Template> templates);
    double CalculateMatchScore(MatchResult result);
}
```

#### 5.4 通信控制接口
```csharp
// Modbus通信接口
interface IModbusService
{
    Task<bool> ConnectAsync(string portName, int baudRate);
    Task DisconnectAsync();
    Task<bool> WriteRegistersAsync(int startAddress, ushort[] values);
    Task<ushort[]> ReadRegistersAsync(int startAddress, int count);
    bool IsConnected { get; }
    event EventHandler<CommunicationEventArgs> CommunicationStatusChanged;
}

// PLC控制接口
interface IPLCControlService
{
    Task SendProductStatusAsync(ProductStatus status);
    Task SendDetectionResultAsync(DetectionResult result);
    Task SendSystemStatusAsync(SystemStatus status);
    Task<PLCStatus> GetPLCStatusAsync();
}
```

#### 5.5 数据管理接口
```csharp
// 生产日志接口
interface IProductionLogService
{
    Task LogProductionDataAsync(ProductionData data);
    Task<List<ProductionData>> GetProductionLogsAsync(DateTime startTime, DateTime endTime);
    Task<ProductionStatistics> GetStatisticsAsync(DateTime startTime, DateTime endTime);
    Task CleanupOldLogsAsync(DateTime cutoffDate);
}

// 系统日志接口
interface ISystemLogService
{
    Task LogInfoAsync(string module, string message);
    Task LogWarningAsync(string module, string message);
    Task LogErrorAsync(string module, string message, Exception exception);
    Task<List<SystemLog>> GetSystemLogsAsync(DateTime startTime, DateTime endTime, LogLevel level);
}

// 配置管理接口
interface IConfigurationService
{
    Task<T> GetConfigurationAsync<T>(string key);
    Task SetConfigurationAsync<T>(string key, T value);
    Task<Dictionary<string, object>> GetAllConfigurationsAsync();
    Task SaveConfigurationsAsync();
    Task ResetToDefaultAsync();
}
```

### 6. 逻辑开发说明

#### 6.1 MVVM架构实现

##### 6.1.1 ViewModel基类设计
```csharp
public abstract class ViewModelBase : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;
    
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
```

##### 6.1.2 命令实现
```csharp
public class RelayCommand : ICommand
{
    private readonly Action<object> _execute;
    private readonly Func<object, bool> _canExecute;
    
    public RelayCommand(Action<object> execute, Func<object, bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged
    {
        add { CommandManager.RequerySuggested += value; }
        remove { CommandManager.RequerySuggested -= value; }
    }
    
    public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
    public void Execute(object parameter) => _execute(parameter);
}
```

#### 6.2 依赖注入配置
```csharp
public class ServiceConfiguration
{
    public static void ConfigureServices(IServiceCollection services)
    {
        // 注册服务
        services.AddSingleton<ICameraService, CameraService>();
        services.AddSingleton<IImageProcessingService, ImageProcessingService>();
        services.AddSingleton<ITemplateService, TemplateService>();
        services.AddSingleton<IModbusService, ModbusService>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        
        // 注册ViewModel
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<CameraSettingsViewModel>();
        services.AddTransient<TemplateManagementViewModel>();
        services.AddTransient<SortingRunViewModel>();
    }
}
```

#### 6.3 异步操作处理
```csharp
public class AsyncOperationHelper
{
    public static async Task ExecuteWithProgressAsync<T>(
        Func<IProgress<ProgressInfo>, Task<T>> operation,
        Action<T> onSuccess,
        Action<Exception> onError,
        IProgress<ProgressInfo> progress = null)
    {
        try
        {
            var result = await operation(progress);
            onSuccess?.Invoke(result);
        }
        catch (Exception ex)
        {
            onError?.Invoke(ex);
        }
    }
}
```

#### 6.4 状态管理
```csharp
public class ApplicationStateManager
{
    private readonly Dictionary<string, object> _state = new();
    
    public T GetState<T>(string key, T defaultValue = default)
    {
        return _state.TryGetValue(key, out var value) ? (T)value : defaultValue;
    }
    
    public void SetState<T>(string key, T value)
    {
        _state[key] = value;
        StateChanged?.Invoke(key, value);
    }
    
    public event Action<string, object> StateChanged;
}
```

### 7. 响应式设计

#### 7.1 自适应布局
- 使用Grid和DockPanel实现弹性布局
- 支持窗口大小调整时的自动适配
- 关键控件的最小尺寸限制

#### 7.2 多分辨率支持
- 矢量图标使用，支持高DPI显示
- 字体大小自适应调整
- 控件间距比例化设计

#### 7.3 主题支持
- 浅色/深色主题切换
- 自定义颜色方案
- 工业化配色设计

### 8. 用户体验优化

#### 8.1 操作引导
- 首次使用向导
- 操作步骤提示
- 快捷键支持

#### 8.2 错误处理
- 友好的错误提示信息
- 操作建议和解决方案
- 错误恢复机制

#### 8.3 性能优化
- 虚拟化列表控件
- 图像显示优化
- 内存使用监控

### 9. 可访问性设计

#### 9.1 键盘导航
- Tab键顺序设计
- 快捷键定义
- 焦点可视化

#### 9.2 视觉辅助
- 高对比度支持
- 字体大小调节
- 颜色盲友好设计

### 10. 测试和验证

#### 10.1 UI自动化测试
- 控件功能测试
- 界面响应测试
- 兼容性测试

#### 10.2 用户体验测试
- 可用性测试
- 性能测试
- 压力测试