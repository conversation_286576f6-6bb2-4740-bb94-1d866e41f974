---
type: "manual"
---

# 开发前工作

 开始开发前，请阅读Development_Documents文件夹内的readme.md文件，找到相应的开发文档，和需要的开发指导文件，阅读并根据实际要求继续开发

# MCP工具使用

 使用 sequential thinking工具细化开发流程，若有建议和问题，通过mcp-feedback-enhanced工具，用问答的方式与我沟通

# 开发规则
 
1. 开发开始前，请仔细分析程序已经完成的工作。
2. 更新开发日志文件夹"./Development_Documents/Development_Logs/"，每完成一个阶段任务，创建新的任务开发日志，记录每一步的开发文档细节。
3. 功能完成后需要仔细审核，查验代码结构，语法等有没有错误，库函数等有没有正确安装配置。如果需要手动安装，请第一时间与我沟通，并提供安装方法。
4. 开发完成后，更新进度到Tasks.md文档
5. 开发每个函数，必须做好中文备注
6. 当解决错误发现死循环时，请工具告知我，并给出解决建议。

**请务必严格按以上规则设计和开发代码**
