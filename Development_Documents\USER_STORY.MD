# 用户故事

## 需求描述

### 项目核心功能

本项目为机器视觉筛选程序，核心功能如下：

- 创建产品模板；
- 通过识别产品与模板的轮廓进行匹配，依据匹配结果完成产品筛选，返回产品状态；
- 通过串口Modbus RTU协议将产品状态发送给下位机（PLC），控制下位机执行筛选动作。

## 产品特征

产品为长方形金属片，具体特征如下：

- 正面：一侧（中间有开槽）带有数字编码，编码为深雕刻形式，其轮廓可清晰识别，但每个产品的编码均不相同；
- 反面：无数字编码。

## 相机参数

- 品牌：海康工业相机
- 接口：网口
- SDK：海康MVSDK
- MVS安装目录：G:\Program Files\MVS

## 技术栈

- 开发语言：C#
- 框架：WPF（.NET 8.0）
- 数据库：SQLite
- 视觉处理：Halcon 23.11（仅限此视觉库）
- 软件架构：MVVM

## 用户角色：操作员

## 用户操作步骤

1. **程序激活**
   - 打开程序后，首次运行需输入激活码（临时激活码或永久激活码）；
   - 输入临时激活码：提示“您为试用用户，请尽快输入永久激活码”，程序启动100000秒倒计时（倒计时不显示在UI界面，且每次开机从上次关机时剩余时间继续计数）；倒计时结束后启动程序锁，提示“请输入您的永久激活码”；
   - 输入永久激活码：提示“设备成功激活”。

2. **激活码补充输入**
   - 试用用户（使用临时激活码）可在“设置”中输入永久激活码，输入成功后提示“设备成功激活”。

3. **相机设置与连接**
   - 程序激活后，用户可在“设置相机”中配置相机IP地址；
   - 配置完成后点击“连接相机”，连接成功提示“相机连接成功”，UI界面的相机控件实时显示相机画面。

4. **参数设置与拍照**
   - 操作员将需创建模板的产品放置在相机实时画面的合适位置；
   - 设置曝光时间（单位：μs）和增益（范围：0-15），点击“拍照”，UI界面的相机控件显示拍照画面。

5. **特征识别**
   - 操作员在拍照画面中，通过ROI工具框选产品的识别区域，点击“识别”；
   - 程序自动识别产品特征，包括产品轮廓及数字编码的位置（中间槽的上方或下方）。

6. **模板保存**
   - 识别完成后，操作员在拍照画面中框选检测区域，输入模板名称并点击“保存模板”；
   - 保存成功后提示“模板保存成功”，且该模板会显示在模板列表中。

7. **模板管理**
   - 操作员可对模板进行管理，包括删除、修改、导出、导入模板。

8. **筛选运行**
   - 操作员在UI界面的模板界面中选择目标模板，设置匹配阈值和拍照间隔时间，点击“开始”；
   - 相机按设定时间拍摄，自动识别产品特征并与模板匹配，输出产品状态。

9. **下位机控制**
   - 程序根据视觉处理结果，通过与下位机（PLC）通讯，控制其执行筛选动作。

10. **筛选动作控制**
    - 操作员可在UI界面中对筛选动作进行开始、暂停、停止操作。

11. **生产日志查看与管理**
    - 操作员可按时间查询生产日志，用于查看生产记录；
    - 生产日志会自动删除1年前的数据。

12. **程序日志查看与管理**
    - 操作员可按时间查询程序日志，用于查看程序运行情况；
    - 程序日志会自动删除1年前的数据。

13. **程序防复制机制**
    - 程序设有MAC锁，防止操作员将程序复制到其他设备上使用（避免恶意复制）。
