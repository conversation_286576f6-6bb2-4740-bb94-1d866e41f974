{"Version": 1, "WorkspaceRootPath": "F:\\Project\\C#_project\\vison\\vision1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vison\\vision1\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vison\\vision1\\vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:vision1.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|f:\\project\\c#_project\\vison\\vision1\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{5C75EB2F-A7AF-4C5F-8366-985B3E5FC03C}|vision1.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vison\\vision1\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "F:\\Project\\C#_project\\vison\\vision1\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-09T03:40:37.862Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "F:\\Project\\C#_project\\vison\\vision1\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "F:\\Project\\C#_project\\vison\\vision1\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-09T03:40:12.437Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "vision1", "DocumentMoniker": "F:\\Project\\C#_project\\vison\\vision1\\vision1.csproj", "RelativeDocumentMoniker": "vision1.csproj", "ToolTip": "F:\\Project\\C#_project\\vison\\vision1\\vision1.csproj", "RelativeToolTip": "vision1.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-09T03:39:50.464Z", "EditorCaption": ""}]}]}]}