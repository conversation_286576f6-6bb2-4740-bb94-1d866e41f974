# 产品需求文档 (PRD)
## 机器视觉筛选程序

### 1. 项目概述

#### 1.1 项目名称
机器视觉筛选程序 (Vision Sorting System)

#### 1.2 项目目标
开发一套基于机器视觉的工业自动化筛选系统，通过图像识别技术对长方形金属片产品进行质量检测和分类，并通过Modbus RTU协议控制下位机PLC执行相应的筛选动作。

#### 1.3 项目背景
- 应用场景：工业自动化生产线
- 检测对象：长方形金属片产品
- 生产节拍：30片/分钟 (2秒/片)
- 运行模式：24小时连续运行
- 精度要求：±1mm

### 2. 产品特征描述

#### 2.1 检测产品规格
- **产品类型**：长方形金属片
- **产品特征**：
  - 正面：一侧中间有开槽，槽的上方或下方带有数字编码
  - 反面：无数字编码
  - 数字编码：深雕刻形式，轮廓清晰可识别
  - 编码特点：每个产品编码不同，字符数量不固定，无固定格式

#### 2.2 识别要求
- **主要识别内容**：产品轮廓特征
- **辅助识别内容**：数字编码位置特征（仅用于位置判断，不存储编码内容）
- **识别精度**：±1mm
- **匹配方式**：轮廓匹配，无需二次验证

### 3. 技术架构

#### 3.1 开发技术栈
- **开发语言**：C#
- **UI框架**：WPF (.NET 8.0)
- **架构模式**：MVVM
- **数据库**：SQLite
- **视觉处理库**：Halcon 23.11
- **通信协议**：Modbus RTU

#### 3.2 硬件配置
- **相机**：海康工业相机 (500万像素)
- **接口**：网口连接
- **控制方式**：通过Halcon算子集成，不使用海康SDK
- **通信**：串口Modbus RTU与PLC通信

### 4. 功能需求

#### 4.1 系统激活功能
- **激活码验证**：
  - 支持临时激活码和永久激活码
  - 临时激活码：100000秒倒计时，后台计时不显示，程序开启时吗，由上次关机时剩下的时间继续倒计时
  - 永久激活码：无时间限制
  - 支持试用期内升级为永久激活
- **防复制机制**：
  - MAC地址锁定
  - 防止程序在其他设备上运行

#### 4.2 相机控制功能
- **连接管理**：
  - IP地址配置
  - 连接状态监控
  - 自动重连机制
- **参数设置**：
  - 曝光时间调节 (单位：μs)
  - 增益调节 (范围：0-15)
  - 实时参数预览
- **图像采集**：
  - 实时预览显示
  - 手动拍照功能
  - 定时自动拍照

#### 4.3 模板管理功能
- **模板创建**：
  - ROI区域选择工具
  - 产品特征自动识别
  - 数字编码位置检测
  - 检测区域框选
- **模板操作**：
  - 模板保存和命名
  - 模板删除和修改
  - 模板导入和导出
  - 模板列表管理
- **模板类型**：
  - 支持创建不同数字串位置的模板
  - 每个模板对应特定的产品特征

#### 4.4 筛选运行功能
- **运行控制**：
  - 模板选择
  - 匹配阈值设置
  - 拍照间隔时间设置
  - 开始/暂停/停止控制
- **实时处理**：
  - 自动图像采集
  - 实时特征识别
  - 模板匹配计算
  - 结果状态输出
- **结果显示**：
  - 实时匹配结果
  - 统计信息显示
  - 处理状态监控

#### 4.5 PLC通信功能
- **通信协议**：Modbus RTU
- **数据格式**：
  ```
  地址1000：产品检测状态（0=无产品，1=合格，2=不合格）
  地址1001：检测结果置位（0=无效，1=新结果）
  地址1002：当前模板ID（1-99）
  地址1003：匹配度百分比（0-100）
  地址1004：系统状态（0=停止，1=运行，2=暂停，3=故障）
  地址1005：错误代码（0=无错误，>0=错误代码）
  地址1006：处理时间（毫秒）
  地址1007：心跳计数器（递增计数）
  ```
- **通信管理**：
  - 串口参数配置
  - 连接状态监控
  - 异常处理和重连

#### 4.6 数据管理功能
- **生产日志**：
  - 按时间记录生产数据
  - 包含检测结果、模板信息、时间戳
  - 支持时间范围查询
  - 自动删除1年前数据
- **程序日志**：
  - 记录程序运行状态
  - 异常信息记录
  - 操作日志记录
  - 自动删除1年前数据
- **配置管理**：
  - 系统参数持久化存储
  - 配置导入导出
  - 默认配置恢复

### 5. 性能需求

#### 5.1 处理性能
- **处理速度**：单次检测时间 < 2秒
- **生产节拍**：30片/分钟
- **识别精度**：±1mm
- **系统稳定性**：24小时连续运行

#### 5.2 系统资源
- **内存使用**：< 2GB
- **CPU占用**：< 80%
- **存储空间**：日志和数据 < 10GB/年
- **响应时间**：UI操作响应 < 500ms

### 6. 用户界面需求

#### 6.1 界面布局
- **主界面**：导航式布局，功能模块清晰分离
- **操作界面**：直观易用，操作流程引导
- **状态显示**：实时状态反馈，异常提示明显
- **数据展示**：表格和图表结合，支持数据筛选

#### 6.2 用户体验
- **操作简便**：最少点击完成常用操作
- **视觉反馈**：操作结果即时反馈
- **错误处理**：友好的错误提示和处理建议
- **帮助系统**：操作指导和问题解答

### 7. 安全需求

#### 7.1 软件安全
- **激活验证**：防止未授权使用
- **硬件锁定**：MAC地址绑定
- **数据保护**：配置和日志数据加密存储

#### 7.2 运行安全
- **异常处理**：完善的异常捕获和恢复机制
- **数据备份**：关键数据自动备份
- **系统监控**：运行状态实时监控

### 8. 兼容性需求

#### 8.1 系统兼容性
- **操作系统**：Windows 10/11 (x64)
- **.NET版本**：.NET 8.0
- **Halcon版本**：Halcon 23.11

#### 8.2 硬件兼容性
- **相机**：海康工业相机系列
- **通信**：标准RS485/RS232串口
- **PLC**：支持Modbus RTU协议的PLC设备

### 9. 质量需求

#### 9.1 可靠性
- **系统可用性**：99.5%
- **故障恢复时间**：< 30秒
- **数据完整性**：100%

#### 9.2 可维护性
- **模块化设计**：功能模块独立
- **日志完整性**：详细的操作和错误日志
- **配置灵活性**：参数可配置和调整

### 10. 项目约束

#### 10.1 技术约束
- 必须使用Halcon 23.11视觉库
- 不得使用海康相机SDK
- 必须采用MVVM架构模式
- 数据库限定为SQLite

#### 10.2 业务约束
- 24小时连续运行要求
- 2秒/片的处理速度要求
- ±1mm的识别精度要求
- MAC锁防复制要求

### 11. 验收标准

#### 11.1 功能验收
- 所有功能模块正常运行
- 识别精度达到±1mm要求
- PLC通信稳定可靠
- 24小时连续运行测试通过

#### 11.2 性能验收
- 处理速度满足30片/分钟要求
- 系统资源占用在合理范围内
- UI响应时间 < 500ms
- 系统稳定性达到99.5%

#### 11.3 安全验收
- 激活码验证功能正常
- MAC锁防复制功能有效
- 异常处理机制完善
- 数据安全保护到位